import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from '../../../common/constants/event-names';
import { PrivateMessageSentEvent } from '../../../common/events/private-message.events';
import { PrivateMessageProducer } from '../../../infrastructure/rabbitmq/producers/private-message.producer';

@Injectable()
export class PrivateMessageSentHandler {
  private readonly logger = new Logger(PrivateMessageSentHandler.name);

  constructor(
    private readonly privateMessageProducer: PrivateMessageProducer,
  ) {}

  @OnEvent(EVENT_NAMES.PRIVATE_MESSAGE_SENT, { async: true })
  async handle(event: PrivateMessageSentEvent) {
    try {
      await this.privateMessageProducer.sendPrivateMessageDeliveryTask({
        messageId: event.messageId,
        senderId: event.senderId,
        receiverId: event.receiverId,
        memberNotificationInfo: event.memberNotificationInfo,
        notificationPayload: event.notificationPayload,
        retryCount: event.retryCount,
      });

      this.logger.log(
        `Private message ${event.messageId} queued for delivery from ${event.senderId} to ${event.receiverId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to queue private message ${event.messageId} for delivery`,
        error,
      );
      throw error;
    }
  }
}
