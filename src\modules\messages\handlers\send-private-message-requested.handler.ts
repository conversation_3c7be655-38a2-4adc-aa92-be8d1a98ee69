import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { SendPrivateMessageRequestedEvent } from 'src/common/events/private-message.events';
import { PrivateMessageService } from '../services/pvt-messages.service';
import { SendPrivateMessageDto } from '../dto/send-private-message.dto';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';

// modules/message/handlers/send-private-message-requested.handler.ts
@Injectable()
export class SendPrivateMessageRequestedHandler {
  private readonly logger = new Logger(SendPrivateMessageRequestedHandler.name);

  constructor(private readonly privateMessageService: PrivateMessageService) {}

  @OnEvent(EVENT_NAMES.SEND_PRIVATE_MESSAGE_REQUESTED, { async: true })
  async handle(event: SendPrivateMessageRequestedEvent) {
    try {
      // Parse the chatId to get sender and receiver IDs
      const parts = event.payload.chatId.split('_').map(Number);
      if (parts.length !== 2 || parts.some(isNaN)) {
        throw new Error(
          'Invalid private chat ID format. Expected: "memberId1_memberId2"',
        );
      }

      // Determine receiver ID (the one that's not the sender)
      const receiverId = parts.find((id) => id !== event.payload.senderId);
      if (!receiverId) {
        throw new Error('Could not determine receiver ID from chat ID');
      }

      const SendPrivateMessageDto: SendPrivateMessageDto = {
        chatType: ChatType.PRIVATE,
        chatId: event.payload.chatId,
        senderId: event.payload.senderId,
        receiverId: receiverId,
        encryptedContent: event.payload.content,
        nonce: event.payload.metadata?.nonce || 'default-nonce',
        encryptedMetaData: event.payload.metadata,
        replyToMessageId: event.payload.replyToMessageId
          ? parseInt(event.payload.replyToMessageId)
          : undefined,
        sentAt: new Date(),
        content: event.payload.content,
        metadata: event.payload.metadata,
        tempId: { tempId: null },
      };

      await this.privateMessageService.saveAndBroadcastMessage(
        SendPrivateMessageDto,
        true, // exclude sender from notifications
      );

      this.logger.log(
        `Private message processed for sender ${event.payload.senderId} to receiver ${receiverId}`,
      );
    } catch (error) {
      this.logger.error(
        'Failed to process send private message request:',
        error,
      );
      throw error;
    }
  }
}
