// Exchanges
export const OTP_EXCHANGE = 'otp_exchange';
export const MESSAGE_EXCHANGE = 'message_exchange';
export const NOTIFICATION_EXCHANGE = 'notification_exchange';
export const OTP_DLQ_EXCHANGE = 'otp_dlq_exchange';
export const MESSAGE_DLQ_EXCHANGE = 'message.dlq.exchange';
export const PRIVATE_MESSAGE_DLQ_EXCHANGE = 'private_message.dlq.exchange';

export const MAX_RETRY_COUNT = 3;

// Queues
export const OTP_QUEUE = 'otp_queue';
export const OTP_DLQ_QUEUE = 'otp_dlq_queue';
export const MESSAGE_DELIVERY_QUEUE = 'message.delivery.queue';
export const MESSAGE_DLQ_QUEUE = 'message_delivery_dlq_queue';
export const PRIVATE_MESSAGE_DELIVERY_QUEUE = 'private_message.delivery.queue';
export const PRIVATE_MESSAGE_DLQ_QUEUE = 'private_message_dlq_queue';


// Routing Keys
export const OTP_SEND_ROUTING_KEY = 'otp.send';
export const OTP_FAILED_ROUTING_KEY = 'otp.failed';
export const MESSAGE_DELIVERY_ROUTING_KEY = 'message.send';
export const MESSAGE_FAILED_ROUTING_KEY = 'message.failed';
export const PRIVATE_MESSAGE_DELIVERY_ROUTING_KEY = 'private_message.send';
export const PRIVATE_MESSAGE_FAILED_ROUTING_KEY = 'private_message.failed';