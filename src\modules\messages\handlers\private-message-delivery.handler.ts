import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import {
  PrivateMessageDeliveryAttemptEvent,
  PrivateMessageDeliveryFailedEvent,
  PrivateMessageDeliverySuccessEvent,
} from 'src/common/events/private-message.events';
import { PrivateMessage } from '../entities/private-message.entity';

@Injectable()
export class PrivateMessageDeliveryHandler {
  private readonly logger = new Logger(PrivateMessageDeliveryHandler.name);

  constructor(
    @InjectRepository(PrivateMessage)
    private readonly privateMessageRepo: Repository<PrivateMessage>,
  ) {}

  @OnEvent(EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_ATTEMPT, { async: true })
  async handleDeliveryAttempt(event: PrivateMessageDeliveryAttemptEvent) {
    this.logger.debug(`Private message ${event.messageId} delivery attempted`);
  }

  @OnEvent(EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_FAILED, { async: true })
  async handleDeliveryFailed(event: PrivateMessageDeliveryFailedEvent) {
    this.logger.warn(
      `Private message ${event.messageId} delivery failed: ${event.reason}`,
    );

    await this.privateMessageRepo.update(event.messageId, {
      deliveredCount: -1, // Mark as permanently failed
    });
  }

  @OnEvent(EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_SUCCESS, { async: true })
  async handleDeliverySuccess(event: PrivateMessageDeliverySuccessEvent) {
    this.logger.log(
      `Private message ${event.messageId} delivered successfully`,
    );

    await this.privateMessageRepo.update(event.messageId, {
      deliveredCount: event.deliveredToCount,
    });
  }
}
