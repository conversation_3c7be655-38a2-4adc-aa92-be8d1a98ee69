import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { EventBus } from '@nestjs/event-bus';
import {
  MESSAGE_EXCHANGE,
  PRIVATE_MESSAGE_DELIVERY_ROUTING_KEY,
  PRIVATE_MESSAGE_DELIVERY_QUEUE,
  MAX_RETRY_COUNT,
} from 'src/common/constants';
import {
  PrivateMessageDeliveryAttemptEvent,
  PrivateMessageDeliveryFailedEvent,
  PrivateMessageDeliverySuccessEvent,
} from 'src/common/events/private-message.events';
import { PrivateMessageDeliveryTaskPayload } from '../producers/private-message.producer';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';

@Injectable()
export class PrivateMessageDeliveryConsumer {
  private readonly logger = new Logger(PrivateMessageDeliveryConsumer.name);

  constructor(
    private readonly eventBus: EventBus,
    private readonly notificationService: NotificationService,
  ) {}

  @RabbitSubscribe({
    exchange: MESSAGE_EXCHANGE,
    routingKey: PRIVATE_MESSAGE_DELIVERY_ROUTING_KEY,
    queue: PRIVATE_MESSAGE_DELIVERY_QUEUE,
    queueOptions: {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'private-message-dlx',
        'x-dead-letter-routing-key': 'private-message-failed',
        'x-message-ttl': 300000, // 5 minutes
      },
    },
  })
  async handlePrivateMessageDelivery(payload: PrivateMessageDeliveryTaskPayload) {
    try {
      this.eventBus.publish(
        new PrivateMessageDeliveryAttemptEvent(payload.messageId),
      );

      this.logger.debug(
        `Processing private message delivery: ${payload.messageId} from ${payload.senderId} to ${payload.receiverId}`,
      );

      // Use the notification service to handle delivery with room validation
      await this.notificationService.broadcastPrivateChatMessageWithValidation(
        payload.senderId,
        payload.receiverId,
        'private_message_received',
        payload.notificationPayload.data,
        payload.notificationPayload,
      );

      this.eventBus.publish(
        new PrivateMessageDeliverySuccessEvent(payload.messageId, 1),
      );

      this.logger.log(
        `Private message ${payload.messageId} delivered successfully`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to deliver private message ${payload.messageId}:`,
        error,
      );

      if (payload.retryCoun >= MAX_RETRY_COUNT) {
        this.eventBus.publish(
          new PrivateMessageDeliveryFailedEvent(
            payload.messageId,
            'Max retry count exceeded',
          ),
        );
      } else {
       
        throw error; 
      }
    }
  }
}